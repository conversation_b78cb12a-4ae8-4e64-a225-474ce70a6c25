"""Add documents table

Revision ID: 7c94917d7c66
Revises: 6380a3195d5b
Create Date: 2025-09-25 00:10:07.823367

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7c94917d7c66'
down_revision = '6380a3195d5b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('documents',
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('document_type', sa.Enum('BIRTH_CERTIFICATE', 'PASSPORT', 'NATIONAL_ID', 'MEDICAL_RECORD', 'ACADEMIC_TRANSCRIPT', 'IMMUNIZATION_RECORD', 'PHOTO', 'PARENT_ID', 'PROOF_OF_ADDRESS', 'OTHER', name='documenttype'), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'APPROVED', 'REJECTED', 'EXPIRED', name='documentstatus'), nullable=False),
    sa.Column('file_name', sa.String(length=255), nullable=False),
    sa.Column('original_file_name', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_size', sa.Integer(), nullable=False),
    sa.Column('mime_type', sa.String(length=100), nullable=False),
    sa.Column('uploaded_by', sa.String(length=36), nullable=False),
    sa.Column('student_id', sa.String(length=36), nullable=False),
    sa.Column('verified_by', sa.String(length=36), nullable=True),
    sa.Column('verified_at', sa.DateTime(), nullable=True),
    sa.Column('verification_notes', sa.Text(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=False),
    sa.Column('school_id', sa.String(length=36), nullable=False),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['school_id'], ['schools.id'], ),
    sa.ForeignKeyConstraint(['student_id'], ['students.id'], ),
    sa.ForeignKeyConstraint(['uploaded_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['verified_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_index(op.f('ix_school_ownerships_is_primary_owner'), table_name='school_ownerships')
    op.drop_index(op.f('ix_school_ownerships_school_id'), table_name='school_ownerships')
    op.drop_index(op.f('ix_school_ownerships_user_id'), table_name='school_ownerships')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_school_ownerships_user_id'), 'school_ownerships', ['user_id'], unique=False)
    op.create_index(op.f('ix_school_ownerships_school_id'), 'school_ownerships', ['school_id'], unique=False)
    op.create_index(op.f('ix_school_ownerships_is_primary_owner'), 'school_ownerships', ['is_primary_owner'], unique=False)
    op.drop_table('documents')
    # ### end Alembic commands ###
